{"extends": "./tsconfig.json", "compilerOptions": {"declaration": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowSyntheticDefaultImports": true, "target": "esnext", "composite": true, "baseUrl": ".", "paths": {"prisma-client": ["../prisma/generated"]}}, "include": ["src/**/*.ts", "../prisma/generated/client.d.ts"], "exclude": ["jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts"]}