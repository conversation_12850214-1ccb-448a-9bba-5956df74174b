import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { FcmProvider } from './fcm.provider';
import {
  NotificationChannel,
  NotificationStatus,
} from '../../shared/interfaces/notification.interface';

describe('FcmProvider', () => {
  let provider: FcmProvider;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FcmProvider,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              const config = {
                FCM_PROJECT_ID: 'test-project',
                FCM_PRIVATE_KEY: 'test-private-key',
                FCM_CLIENT_EMAIL: '<EMAIL>',
              };
              return config[key];
            }),
          },
        },
      ],
    }).compile();

    provider = module.get<FcmProvider>(FcmProvider);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(provider).toBeDefined();
  });

  it('should have FCM channel', () => {
    expect(provider.channel).toBe(NotificationChannel.FCM);
  });

  describe('validateRecipient', () => {
    it('should validate FCM recipient correctly', () => {
      const validRecipient = {
        id: 'test-recipient',
        channel: NotificationChannel.FCM,
        address: 'valid-fcm-token',
      };

      expect(provider.validateRecipient(validRecipient)).toBe(true);
    });

    it('should reject invalid channel', () => {
      const invalidRecipient = {
        id: 'test-recipient',
        channel: NotificationChannel.EMAIL,
        address: 'valid-fcm-token',
      };

      expect(provider.validateRecipient(invalidRecipient)).toBe(false);
    });

    it('should reject empty address', () => {
      const invalidRecipient = {
        id: 'test-recipient',
        channel: NotificationChannel.FCM,
        address: '',
      };

      expect(provider.validateRecipient(invalidRecipient)).toBe(false);
    });
  });

  describe('send', () => {
    it('should send notification successfully', async () => {
      const payload = {
        title: 'Test Title',
        body: 'Test Body',
      };

      const recipient = {
        id: 'test-recipient',
        channel: NotificationChannel.FCM,
        address: 'valid-fcm-token',
      };

      const result = await provider.send(payload, recipient);

      expect(result.recipientId).toBe(recipient.id);
      expect(result.channel).toBe(NotificationChannel.FCM);
      expect(result.status).toBe(NotificationStatus.SENT);
    });

    it('should handle invalid payload', async () => {
      const payload = {
        title: '',
        body: '',
      };

      const recipient = {
        id: 'test-recipient',
        channel: NotificationChannel.FCM,
        address: 'valid-fcm-token',
      };

      const result = await provider.send(payload, recipient);

      expect(result.status).toBe(NotificationStatus.FAILED);
      expect(result.failureReason).toContain('Invalid payload');
    });
  });

  describe('isHealthy', () => {
    it('should return health status', async () => {
      const isHealthy = await provider.isHealthy();
      expect(typeof isHealthy).toBe('boolean');
    });
  });
});
