import { Injectable, Logger } from '@nestjs/common';
import type {
  NotificationRecipient,
  NotificationResult,
} from '../../shared/interfaces/notification.interface';
import { NotificationChannel } from '../../shared/enums';
import { NotificationStatus } from '../../shared/enums/notification-status.enum';
import type { INotificationProvider } from '../../shared/interfaces/notification-provider.interface';
import { v4 as uuidv4 } from 'uuid';
import type { INotificationMessage } from '../../shared';

@Injectable()
export abstract class NotificationProvider<T extends INotificationMessage>
  implements INotificationProvider<T>
{
  protected readonly logger = new Logger(this.constructor.name);

  abstract readonly channel: NotificationChannel;

  abstract send(payload: T): Promise<NotificationResult>;

  abstract validateRecipient(recipient: NotificationRecipient): boolean;

  abstract isHealthy(): Promise<boolean>;

  protected createResult(
    recipientId: string,
    status: NotificationStatus,
    metadata?: Record<string, any>,
    failureReason?: string
  ): NotificationResult {
    return {
      id: uuidv4(),
      recipientId,
      channel: this.channel,
      status,
      sentAt: status === NotificationStatus.Sent ? new Date() : undefined,
      deliveredAt: status === NotificationStatus.Delivered ? new Date() : undefined,
      failureReason,
      metadata,
    };
  }

  protected async handleError(error: any, recipientId: string): Promise<NotificationResult> {
    this.logger.error(`Failed to send notification via ${this.channel}`, error);
    return this.createResult(
      recipientId,
      NotificationStatus.Failed,
      { error: error.message },
      error.message
    );
  }

  protected validatePayload(payload: T): boolean {
    return !!(payload.getSubject() && payload.getContent());
  }
}
