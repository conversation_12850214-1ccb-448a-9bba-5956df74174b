import {
  type INotificationMessage,
  NotificationChannel,
  type NotificationRecipient,
} from '../../shared';
import Handlebars from 'handlebars';

export abstract class NotificationMessage implements INotificationMessage {
  /**
   *
   */
  constructor(
    private readonly subject: string,
    private readonly content: string,
    private readonly data?: Record<string, any>
  ) {
    this.subject = this.renderTemplate(this.subject, this.data ?? {});
    this.content = this.renderTemplate(this.subject, this.data ?? {});
  }

  getContent(): string {
    return this.content;
  }

  getSubject(): string {
    return this.subject;
  }

  getRecipient(): NotificationRecipient {
    throw new Error('Method not implemented.');
  }

  renderTemplate(template: string, data: Record<string, any>): string {
    const compiledTemplate = Handlebars.compile(template);
    return compiledTemplate(data);
  }

  abstract readonly channel: NotificationChannel;

  abstract validateRecipient(recipient: NotificationRecipient): boolean;
}
