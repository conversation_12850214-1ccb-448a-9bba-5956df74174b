import { NotificationChannel } from '../enums';
import type { INotificationMessage, NotificationRecipient, NotificationResult } from '.';

export interface INotificationProvider<T extends INotificationMessage> {
  readonly channel: NotificationChannel;
  send(payload: T): Promise<NotificationResult>;
  validateRecipient(recipient: NotificationRecipient): boolean;
  isHealthy(): Promise<boolean>;
}

export const INotificationProvider = Symbol('INotificationProvider');
