import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsEnum, IsOptional, IsString, ValidateNested } from 'class-validator';
import { NotificationChannel } from '../enums';

import { NotificationPayloadDto } from './notification-payload.dto';

export class UpdateTemplateDto {
  @ApiPropertyOptional({ description: 'Template name' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Template description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    enum: NotificationChannel,
    isArray: true,
    description: 'Supported channels',
  })
  @IsOptional()
  @IsArray()
  @IsEnum(NotificationChannel, { each: true })
  channels?: NotificationChannel[];

  @ApiPropertyOptional({ type: NotificationPayloadDto, description: 'Template payload' })
  @IsOptional()
  @ValidateNested()
  @Type(() => NotificationPayloadDto)
  payload?: NotificationPayloadDto;

  @ApiPropertyOptional({ description: 'Template variables for dynamic content' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  variables?: string[];

  @ApiPropertyOptional({ description: 'Template active status' })
  @IsOptional()
  isActive?: boolean;
}
