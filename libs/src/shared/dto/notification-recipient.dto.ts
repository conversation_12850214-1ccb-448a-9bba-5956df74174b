import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsObject, IsOptional, IsString } from 'class-validator';
import { NotificationChannel } from '../enums';

export class NotificationRecipientDto {
  @ApiProperty({ description: 'Unique recipient identifier' })
  @IsString()
  id: string;

  @ApiProperty({ enum: NotificationChannel, description: 'Notification channel' })
  @IsEnum(NotificationChannel)
  channel: NotificationChannel;

  @ApiProperty({ description: 'Recipient address (email, phone, device token)' })
  @IsString()
  address: string;

  @ApiPropertyOptional({ description: 'Additional recipient metadata' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
