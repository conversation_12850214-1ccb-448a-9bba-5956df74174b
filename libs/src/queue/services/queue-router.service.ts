import { Inject, Injectable, Logger } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { NotificationChannel } from '../../shared';
import type { NotificationQueueMessage } from '../interfaces/queue-message.interface';
import { IQueueService } from '../interfaces/queue-service.interface';

@Injectable()
export class QueueRouterService {
  //   private readonly logger = new Logger(QueueRouterService.name);
  //   constructor(@Inject(IQueueService) private readonly queueService: ClientProxy) {}
  //   /**
  //    * Route notification message to the appropriate channel queue
  //    */
  //   async routeNotification(message: NotificationQueueMessage): Promise<void> {
  //     const { notification } = message;
  //     // Group recipients by channel
  //     const recipientsByChannel = this.groupRecipientsByChannel(notification.recipients);
  //     // Send to each channel queue
  //     for (const [channel, recipients] of recipientsByChannel.entries()) {
  //       const channelMessage: NotificationQueueMessage = {
  //         ...message,
  //         notification: {
  //           ...notification,
  //           recipients,
  //           channels: [channel],
  //         },
  //       };
  //       await this.sendToChannelQueue(channel, channelMessage);
  //     }
  //   }
  //   /**
  //    * Send message to specific channel queue
  //    */
  //   private async sendToChannelQueue(
  //     channel: NotificationChannel,
  //     message: NotificationQueueMessage
  //   ): Promise<void> {
  //     try {
  //       const queue = this.getQueueForChannel(channel);
  //       const pattern = this.getPatternForChannel(channel);
  //       await queue.emit(pattern, message).toPromise();
  //       this.logger.log(`Message routed to ${channel} queue: ${message.id}`);
  //     } catch (error) {
  //       this.logger.error(`Failed to route message to ${channel} queue: ${error.message}`);
  //       await this.sendToRetryQueue(message, error.message);
  //     }
  //   }
  //   /**
  //    * Get the message pattern for a channel
  //    */
  //   private getPatternForChannel(channel: NotificationChannel): string {
  //     return `notification.${channel.toLowerCase()}.`;
  //   }
  //   /**
  //    * Group recipients by their notification channel
  //    */
  //   private groupRecipientsByChannel(recipients: any[]): Map<NotificationChannel, any[]> {
  //     const grouped = new Map<NotificationChannel, any[]>();
  //     for (const recipient of recipients) {
  //       const channel = recipient.channel as NotificationChannel;
  //       if (!grouped.has(channel)) {
  //         grouped.set(channel, []);
  //       }
  //       grouped.get(channel)!.push(recipient);
  //     }
  //     return grouped;
  //   }
  //   /**
  //    * Calculate retry delay with exponential backoff
  //    */
  //   private getRetryDelay(retryCount: number): number {
  //     const baseDelay = 1000; // 1 second
  //     const maxDelay = 300000; // 5 minutes
  //     const delay = Math.min(baseDelay * Math.pow(2, retryCount), maxDelay);
  //     // Add jitter to prevent thundering herd
  //     return delay + Math.random() * 1000;
  //   }
  //   /**
  //    * Get all supported channels
  //    */
  //   getSupportedChannels(): NotificationChannel[] {
  //     return [
  //       NotificationChannel.Fcm,
  //       NotificationChannel.Apns,
  //       NotificationChannel.Email,
  //       NotificationChannel.Sms,
  //       NotificationChannel.Wns,
  //     ];
  //   }
  //   /**
  //    * Check if a channel is supported
  //    */
  //   isChannelSupported(channel: string): boolean {
  //     return this.getSupportedChannels().includes(channel as NotificationChannel);
  //   }
}
