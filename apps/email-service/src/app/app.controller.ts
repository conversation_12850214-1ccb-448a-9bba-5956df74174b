import { Controller, Get, Logger } from '@nestjs/common';
import { AppService } from './app.service';
import { EventPattern, Payload } from '@nestjs/microservices';

@Controller()
export class AppController {
  logger = new Logger(AppController.name);
  constructor(private readonly appService: AppService) {}

  @Get()
  getData() {
    return this.appService.getData();
  }

  @EventPattern('notification.fcm.send')
  async handleFcmNotification(@Payload() message: any) {
    this.logger.log(`Processing FCM notification: ${message.id}`);
  }
}
