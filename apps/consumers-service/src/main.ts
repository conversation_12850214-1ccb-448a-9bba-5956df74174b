/**
 * Herond Notification Center - Consumers Service
 * Microservice for processing notification messages from RabbitMQ queues
 */

import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { Transport, type MicroserviceOptions } from '@nestjs/microservices';
import { ConfigService } from '@nestjs/config';

import { AppModule } from './app/app.module';

async function bootstrap() {
  const logger = new Logger('ConsumersService');

  // Create the application context to access ConfigService
  const appContext = await NestFactory.createApplicationContext(AppModule);
  const configService = appContext.get(ConfigService);

  // Create microservice for each queue
  const rabbitmqUrl = configService.get<string>('RABBITMQ_URL', 'amqp://localhost:5672');

  // Create hybrid application (HTTP + Microservice)
  const app = await NestFactory.create(AppModule);

  // Connect to FCM queue
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.RMQ,
    options: {
      urls: [rabbitmqUrl],
      queue: 'notifications.fcm',
      queueOptions: { durable: true, arguments: {} },
      prefetchCount: 10,
    },
  });

  // Connect to APNS queue
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.RMQ,
    options: {
      urls: [rabbitmqUrl],
      queue: 'notifications.apns',
      queueOptions: { durable: true },
      prefetchCount: 10,
    },
  });

  // Connect to Email queue
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.RMQ,
    options: {
      urls: [rabbitmqUrl],
      queue: 'notifications.email',
      queueOptions: { durable: true },
      prefetchCount: 5,
    },
  });

  // Connect to SMS queue
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.RMQ,
    options: {
      urls: [rabbitmqUrl],
      queue: 'notifications.sms',
      queueOptions: { durable: true },
      prefetchCount: 8,
    },
  });

  // Connect to WNS queue
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.RMQ,
    options: {
      urls: [rabbitmqUrl],
      queue: 'notifications.wns',
      queueOptions: { durable: true },
      prefetchCount: 10,
    },
  });

  // Start all microservices
  await app.startAllMicroservices();

  // Also start HTTP server for health checks
  const port = process.env.CONSUMERS_PORT || 3001;
  await app.listen(port);

  logger.log(`🚀 Consumers Service is running on: http://localhost:${port}`);
  logger.log(`📡 Connected to RabbitMQ: ${rabbitmqUrl}`);
  logger.log(`🔄 Processing queues: FCM, APNS, Email, SMS, WNS`);

  await appContext.close();
}

bootstrap().catch(error => {
  Logger.error('Failed to start Consumers Service', error);
  process.exit(1);
});
