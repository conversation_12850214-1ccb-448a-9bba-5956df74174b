import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from '@herond-notification-center/database';
import { QueueModule } from '@herond-notification-center/queue';
import { NotificationProvidersModule } from '@herond-notification-center/notification-providers';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { FcmConsumerService } from './consumers/fcm-consumer.service';
import { ApnsConsumerService } from './consumers/apns-consumer.service';
import { EmailConsumerService } from './consumers/email-consumer.service';
import { SmsConsumerService } from './consumers/sms-consumer.service';
import { WnsConsumerService } from './consumers/wns-consumer.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    DatabaseModule,
    NotificationProvidersModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    FcmConsumerService,
    ApnsConsumerService,
    EmailConsumerService,
    SmsConsumerService,
    WnsConsumerService,
  ],
})
export class AppModule {}
