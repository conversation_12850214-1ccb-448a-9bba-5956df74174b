import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { EventPattern, Payload } from '@nestjs/microservices';
import { SmsProvider } from '@herond-notification-center/notification-providers/sms';
import { NotificationRepository, NotificationResultRepository } from '@herond-notification-center/database';
import { NotificationQueueMessage } from '@herond-notification-center/queue/interfaces/queue-message.interface';
import { NotificationChannel, NotificationStatus } from '@herond-notification-center/shared/interfaces/notification.interface';

@Injectable()
export class SmsConsumerService implements OnModuleInit {
  private readonly logger = new Logger(SmsConsumerService.name);

  constructor(
    private readonly smsProvider: SmsProvider,
    private readonly notificationRepository: NotificationRepository,
    private readonly notificationResultRepository: NotificationResultRepository,
  ) {}

  async onModuleInit() {
    this.logger.log('SMS Consumer Service initialized');
  }

  @EventPattern('notification.sms.send')
  async handleSmsNotification(@Payload() message: NotificationQueueMessage) {
    this.logger.log(`Processing SMS notification: ${message.id}`);

    try {
      const { notification } = message;
      const results = [];

      // Process each SMS recipient
      for (const recipient of notification.recipients) {
        if (recipient.channel !== NotificationChannel.SMS) {
          continue;
        }

        try {
          // Send notification via SMS
          const result = await this.smsProvider.send({
            to: recipient.address,
            body: `${notification.payload.title}\n${notification.payload.body}`,
            from: notification.payload.from || undefined,
          });

          // Create success result
          const notificationResult = await this.notificationResultRepository.create({
            notificationId: message.id,
            recipientId: recipient.id,
            channel: NotificationChannel.SMS,
            status: NotificationStatus.SENT,
            sentAt: new Date(),
            metadata: {
              messageSid: result.sid,
              status: result.status,
              direction: result.direction,
            },
          });

          results.push(notificationResult);
          this.logger.log(`SMS notification sent successfully to ${recipient.address}`);

        } catch (error) {
          this.logger.error(`Failed to send SMS notification to ${recipient.address}: ${error.message}`);

          // Create failure result
          const notificationResult = await this.notificationResultRepository.create({
            notificationId: message.id,
            recipientId: recipient.id,
            channel: NotificationChannel.SMS,
            status: NotificationStatus.FAILED,
            error: error.message,
            metadata: {
              errorCode: error.code,
              errorMessage: error.message,
            },
          });

          results.push(notificationResult);
        }
      }

      // Update notification status
      const hasFailures = results.some(r => r.status === NotificationStatus.FAILED);
      const hasSuccess = results.some(r => r.status === NotificationStatus.SENT);

      let overallStatus: NotificationStatus;
      if (hasSuccess && !hasFailures) {
        overallStatus = NotificationStatus.SENT;
      } else if (hasSuccess && hasFailures) {
        overallStatus = NotificationStatus.PARTIAL;
      } else {
        overallStatus = NotificationStatus.FAILED;
      }

      await this.notificationRepository.updateStatus(message.id, overallStatus);

      this.logger.log(`SMS notification processing completed: ${message.id} - Status: ${overallStatus}`);

    } catch (error) {
      this.logger.error(`Failed to process SMS notification ${message.id}: ${error.message}`);

      // Update notification as failed
      await this.notificationRepository.updateStatus(message.id, NotificationStatus.FAILED);

      // Create failure results for all recipients
      for (const recipient of message.notification.recipients) {
        if (recipient.channel === NotificationChannel.SMS) {
          await this.notificationResultRepository.create({
            notificationId: message.id,
            recipientId: recipient.id,
            channel: NotificationChannel.SMS,
            status: NotificationStatus.FAILED,
            error: error.message,
            metadata: {
              processingError: true,
            },
          });
        }
      }

      throw error; // Re-throw to trigger retry mechanism
    }
  }

  @EventPattern('notification.sms.retry')
  async handleSmsRetry(@Payload() message: any) {
    this.logger.log(`Retrying SMS notification: ${message.originalMessage.id}`);
    
    // Check if we've exceeded max retries
    if (message.retryCount >= (message.originalMessage.maxRetries || 3)) {
      this.logger.error(`Max retries exceeded for SMS notification: ${message.originalMessage.id}`);
      await this.notificationRepository.updateStatus(message.originalMessage.id, NotificationStatus.FAILED);
      return;
    }

    // Retry the notification
    await this.handleSmsNotification(message.originalMessage);
  }
}
