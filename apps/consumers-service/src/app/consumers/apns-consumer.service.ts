import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { EventPattern, Payload } from '@nestjs/microservices';
import { ApnsProvider } from '@herond-notification-center/notification-providers/apns';
import { NotificationRepository, NotificationResultRepository } from '@herond-notification-center/database';
import { NotificationQueueMessage } from '@herond-notification-center/queue/interfaces/queue-message.interface';
import { NotificationChannel, NotificationStatus } from '@herond-notification-center/shared/interfaces/notification.interface';

@Injectable()
export class ApnsConsumerService implements OnModuleInit {
  private readonly logger = new Logger(ApnsConsumerService.name);

  constructor(
    private readonly apnsProvider: ApnsProvider,
    private readonly notificationRepository: NotificationRepository,
    private readonly notificationResultRepository: NotificationResultRepository,
  ) {}

  async onModuleInit() {
    this.logger.log('APNS Consumer Service initialized');
  }

  @EventPattern('notification.apns.send')
  async handleApnsNotification(@Payload() message: NotificationQueueMessage) {
    this.logger.log(`Processing APNS notification: ${message.id}`);

    try {
      const { notification } = message;
      const results = [];

      // Process each APNS recipient
      for (const recipient of notification.recipients) {
        if (recipient.channel !== NotificationChannel.APNS) {
          continue;
        }

        try {
          // Send notification via APNS
          const result = await this.apnsProvider.send({
            deviceToken: recipient.address,
            alert: {
              title: notification.payload.title,
              body: notification.payload.body,
            },
            badge: notification.payload.badge || 0,
            sound: notification.payload.sound || 'default',
            payload: notification.payload.data || {},
            priority: notification.priority === 'high' ? 10 : 5,
            expiry: Math.floor(Date.now() / 1000) + 86400, // 24 hours
          });

          // Create success result
          const notificationResult = await this.notificationResultRepository.create({
            notificationId: message.id,
            recipientId: recipient.id,
            channel: NotificationChannel.APNS,
            status: NotificationStatus.SENT,
            sentAt: new Date(),
            metadata: {
              apnsId: result.id,
              apnsResponse: result,
            },
          });

          results.push(notificationResult);
          this.logger.log(`APNS notification sent successfully to ${recipient.address}`);

        } catch (error) {
          this.logger.error(`Failed to send APNS notification to ${recipient.address}: ${error.message}`);

          // Create failure result
          const notificationResult = await this.notificationResultRepository.create({
            notificationId: message.id,
            recipientId: recipient.id,
            channel: NotificationChannel.APNS,
            status: NotificationStatus.FAILED,
            error: error.message,
            metadata: {
              errorCode: error.code,
              errorReason: error.reason,
            },
          });

          results.push(notificationResult);
        }
      }

      // Update notification status
      const hasFailures = results.some(r => r.status === NotificationStatus.FAILED);
      const hasSuccess = results.some(r => r.status === NotificationStatus.SENT);

      let overallStatus: NotificationStatus;
      if (hasSuccess && !hasFailures) {
        overallStatus = NotificationStatus.SENT;
      } else if (hasSuccess && hasFailures) {
        overallStatus = NotificationStatus.PARTIAL;
      } else {
        overallStatus = NotificationStatus.FAILED;
      }

      await this.notificationRepository.updateStatus(message.id, overallStatus);

      this.logger.log(`APNS notification processing completed: ${message.id} - Status: ${overallStatus}`);

    } catch (error) {
      this.logger.error(`Failed to process APNS notification ${message.id}: ${error.message}`);

      // Update notification as failed
      await this.notificationRepository.updateStatus(message.id, NotificationStatus.FAILED);

      // Create failure results for all recipients
      for (const recipient of message.notification.recipients) {
        if (recipient.channel === NotificationChannel.APNS) {
          await this.notificationResultRepository.create({
            notificationId: message.id,
            recipientId: recipient.id,
            channel: NotificationChannel.APNS,
            status: NotificationStatus.FAILED,
            error: error.message,
            metadata: {
              processingError: true,
            },
          });
        }
      }

      throw error; // Re-throw to trigger retry mechanism
    }
  }

  @EventPattern('notification.apns.retry')
  async handleApnsRetry(@Payload() message: any) {
    this.logger.log(`Retrying APNS notification: ${message.originalMessage.id}`);
    
    // Check if we've exceeded max retries
    if (message.retryCount >= (message.originalMessage.maxRetries || 3)) {
      this.logger.error(`Max retries exceeded for APNS notification: ${message.originalMessage.id}`);
      await this.notificationRepository.updateStatus(message.originalMessage.id, NotificationStatus.FAILED);
      return;
    }

    // Retry the notification
    await this.handleApnsNotification(message.originalMessage);
  }
}
