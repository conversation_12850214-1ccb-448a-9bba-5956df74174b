import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { EventPattern, Payload } from '@nestjs/microservices';
import { WnsProvider } from '@herond-notification-center/notification-providers/wns';
import { NotificationRepository, NotificationResultRepository } from '@herond-notification-center/database';
import { NotificationQueueMessage } from '@herond-notification-center/queue/interfaces/queue-message.interface';
import { NotificationChannel, NotificationStatus } from '@herond-notification-center/shared/interfaces/notification.interface';

@Injectable()
export class WnsConsumerService implements OnModuleInit {
  private readonly logger = new Logger(WnsConsumerService.name);

  constructor(
    private readonly wnsProvider: WnsProvider,
    private readonly notificationRepository: NotificationRepository,
    private readonly notificationResultRepository: NotificationResultRepository,
  ) {}

  async onModuleInit() {
    this.logger.log('WNS Consumer Service initialized');
  }

  @EventPattern('notification.wns.send')
  async handleWnsNotification(@Payload() message: NotificationQueueMessage) {
    this.logger.log(`Processing WNS notification: ${message.id}`);

    try {
      const { notification } = message;
      const results = [];

      // Process each WNS recipient
      for (const recipient of notification.recipients) {
        if (recipient.channel !== NotificationChannel.WNS) {
          continue;
        }

        try {
          // Send notification via WNS
          const result = await this.wnsProvider.send({
            channelUri: recipient.address,
            payload: {
              title: notification.payload.title,
              body: notification.payload.body,
              data: notification.payload.data || {},
            },
            type: notification.payload.type || 'toast',
            priority: notification.priority === 'high' ? 'high' : 'normal',
          });

          // Create success result
          const notificationResult = await this.notificationResultRepository.create({
            notificationId: message.id,
            recipientId: recipient.id,
            channel: NotificationChannel.WNS,
            status: NotificationStatus.SENT,
            sentAt: new Date(),
            metadata: {
              wnsResponse: result,
              notificationStatus: result.notificationStatus,
            },
          });

          results.push(notificationResult);
          this.logger.log(`WNS notification sent successfully to ${recipient.address}`);

        } catch (error) {
          this.logger.error(`Failed to send WNS notification to ${recipient.address}: ${error.message}`);

          // Create failure result
          const notificationResult = await this.notificationResultRepository.create({
            notificationId: message.id,
            recipientId: recipient.id,
            channel: NotificationChannel.WNS,
            status: NotificationStatus.FAILED,
            error: error.message,
            metadata: {
              errorCode: error.code,
              errorDescription: error.description,
            },
          });

          results.push(notificationResult);
        }
      }

      // Update notification status
      const hasFailures = results.some(r => r.status === NotificationStatus.FAILED);
      const hasSuccess = results.some(r => r.status === NotificationStatus.SENT);

      let overallStatus: NotificationStatus;
      if (hasSuccess && !hasFailures) {
        overallStatus = NotificationStatus.SENT;
      } else if (hasSuccess && hasFailures) {
        overallStatus = NotificationStatus.PARTIAL;
      } else {
        overallStatus = NotificationStatus.FAILED;
      }

      await this.notificationRepository.updateStatus(message.id, overallStatus);

      this.logger.log(`WNS notification processing completed: ${message.id} - Status: ${overallStatus}`);

    } catch (error) {
      this.logger.error(`Failed to process WNS notification ${message.id}: ${error.message}`);

      // Update notification as failed
      await this.notificationRepository.updateStatus(message.id, NotificationStatus.FAILED);

      // Create failure results for all recipients
      for (const recipient of message.notification.recipients) {
        if (recipient.channel === NotificationChannel.WNS) {
          await this.notificationResultRepository.create({
            notificationId: message.id,
            recipientId: recipient.id,
            channel: NotificationChannel.WNS,
            status: NotificationStatus.FAILED,
            error: error.message,
            metadata: {
              processingError: true,
            },
          });
        }
      }

      throw error; // Re-throw to trigger retry mechanism
    }
  }

  @EventPattern('notification.wns.retry')
  async handleWnsRetry(@Payload() message: any) {
    this.logger.log(`Retrying WNS notification: ${message.originalMessage.id}`);
    
    // Check if we've exceeded max retries
    if (message.retryCount >= (message.originalMessage.maxRetries || 3)) {
      this.logger.error(`Max retries exceeded for WNS notification: ${message.originalMessage.id}`);
      await this.notificationRepository.updateStatus(message.originalMessage.id, NotificationStatus.FAILED);
      return;
    }

    // Retry the notification
    await this.handleWnsNotification(message.originalMessage);
  }
}
