import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { EventPattern, Payload } from '@nestjs/microservices';
import { EmailProvider } from '@herond-notification-center/notification-providers/email';
import { NotificationRepository, NotificationResultRepository } from '@herond-notification-center/database';
import { NotificationQueueMessage } from '@herond-notification-center/queue/interfaces/queue-message.interface';
import { NotificationChannel, NotificationStatus } from '@herond-notification-center/shared/interfaces/notification.interface';

@Injectable()
export class EmailConsumerService implements OnModuleInit {
  private readonly logger = new Logger(EmailConsumerService.name);

  constructor(
    private readonly emailProvider: EmailProvider,
    private readonly notificationRepository: NotificationRepository,
    private readonly notificationResultRepository: NotificationResultRepository,
  ) {}

  async onModuleInit() {
    this.logger.log('Email Consumer Service initialized');
  }

  @EventPattern('notification.email.send')
  async handleEmailNotification(@Payload() message: NotificationQueueMessage) {
    this.logger.log(`Processing Email notification: ${message.id}`);

    try {
      const { notification } = message;
      const results = [];

      // Process each Email recipient
      for (const recipient of notification.recipients) {
        if (recipient.channel !== NotificationChannel.EMAIL) {
          continue;
        }

        try {
          // Send notification via Email
          const result = await this.emailProvider.send({
            to: recipient.address,
            subject: notification.payload.title,
            text: notification.payload.body,
            html: notification.payload.html || `<p>${notification.payload.body}</p>`,
            attachments: notification.payload.attachments || [],
            priority: notification.priority === 'high' ? 'high' : 'normal',
          });

          // Create success result
          const notificationResult = await this.notificationResultRepository.create({
            notificationId: message.id,
            recipientId: recipient.id,
            channel: NotificationChannel.EMAIL,
            status: NotificationStatus.SENT,
            sentAt: new Date(),
            metadata: {
              messageId: result.messageId,
              response: result.response,
            },
          });

          results.push(notificationResult);
          this.logger.log(`Email notification sent successfully to ${recipient.address}`);

        } catch (error) {
          this.logger.error(`Failed to send Email notification to ${recipient.address}: ${error.message}`);

          // Create failure result
          const notificationResult = await this.notificationResultRepository.create({
            notificationId: message.id,
            recipientId: recipient.id,
            channel: NotificationChannel.EMAIL,
            status: NotificationStatus.FAILED,
            error: error.message,
            metadata: {
              errorCode: error.code,
              errorResponse: error.response,
            },
          });

          results.push(notificationResult);
        }
      }

      // Update notification status
      const hasFailures = results.some(r => r.status === NotificationStatus.FAILED);
      const hasSuccess = results.some(r => r.status === NotificationStatus.SENT);

      let overallStatus: NotificationStatus;
      if (hasSuccess && !hasFailures) {
        overallStatus = NotificationStatus.SENT;
      } else if (hasSuccess && hasFailures) {
        overallStatus = NotificationStatus.PARTIAL;
      } else {
        overallStatus = NotificationStatus.FAILED;
      }

      await this.notificationRepository.updateStatus(message.id, overallStatus);

      this.logger.log(`Email notification processing completed: ${message.id} - Status: ${overallStatus}`);

    } catch (error) {
      this.logger.error(`Failed to process Email notification ${message.id}: ${error.message}`);

      // Update notification as failed
      await this.notificationRepository.updateStatus(message.id, NotificationStatus.FAILED);

      // Create failure results for all recipients
      for (const recipient of message.notification.recipients) {
        if (recipient.channel === NotificationChannel.EMAIL) {
          await this.notificationResultRepository.create({
            notificationId: message.id,
            recipientId: recipient.id,
            channel: NotificationChannel.EMAIL,
            status: NotificationStatus.FAILED,
            error: error.message,
            metadata: {
              processingError: true,
            },
          });
        }
      }

      throw error; // Re-throw to trigger retry mechanism
    }
  }

  @EventPattern('notification.email.retry')
  async handleEmailRetry(@Payload() message: any) {
    this.logger.log(`Retrying Email notification: ${message.originalMessage.id}`);
    
    // Check if we've exceeded max retries
    if (message.retryCount >= (message.originalMessage.maxRetries || 3)) {
      this.logger.error(`Max retries exceeded for Email notification: ${message.originalMessage.id}`);
      await this.notificationRepository.updateStatus(message.originalMessage.id, NotificationStatus.FAILED);
      return;
    }

    // Retry the notification
    await this.handleEmailNotification(message.originalMessage);
  }
}
