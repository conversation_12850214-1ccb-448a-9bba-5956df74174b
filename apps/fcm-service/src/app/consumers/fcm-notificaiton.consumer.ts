import { NotificationService } from '@libs';
import { Controller, Injectable, Logger } from '@nestjs/common';
import { Ctx, EventPattern, MessagePattern, Payload } from '@nestjs/microservices';

@Controller()
export class FcmNotificationConsumer {
  private readonly logger = new Logger(FcmNotificationConsumer.name);

  constructor(
    private readonly notificationService: NotificationService // private readonly notificationRepository: NotificationRepository, // private readonly notificationResultRepository: NotificationResultRepository
  ) {}

  async onModuleInit() {
    this.logger.log('FCM Consumer Service initialized');
  }

  @EventPattern('notification.fcm.low')
  async handleFcmNotification(@Payload() message: any) {
    this.logger.log(`Processing FCM notification: ${message.id}`);

    // try {
    //   const { notification } = message;
    //   const results = [];

    //   // Process each FCM recipient
    //   for (const recipient of notification.recipients) {
    //     if (recipient.channel !== NotificationChannel.FCM) {
    //       continue;
    //     }

    //     try {
    //       // Send notification via FCM
    //       const result = await this.fcmProvider.send({
    //         to: recipient.address,
    //         notification: {
    //           title: notification.payload.title,
    //           body: notification.payload.body,
    //         },
    //         data: notification.payload.data || {},
    //         priority: notification.priority === 'high' ? 'high' : 'normal',
    //         timeToLive: 86400, // 24 hours
    //       });

    //       // Create success result
    //       const notificationResult = await this.notificationResultRepository.create({
    //         notificationId: message.id,
    //         recipientId: recipient.id,
    //         channel: NotificationChannel.FCM,
    //         status: NotificationStatus.SENT,
    //         sentAt: new Date(),
    //         metadata: {
    //           messageId: result.messageId,
    //           fcmResponse: result,
    //         },
    //       });

    //       results.push(notificationResult);
    //       this.logger.log(`FCM notification sent successfully to ${recipient.address}`);
    //     } catch (error) {
    //       this.logger.error(
    //         `Failed to send FCM notification to ${recipient.address}: ${error.message}`
    //       );

    //       // Create failure result
    //       const notificationResult = await this.notificationResultRepository.create({
    //         notificationId: message.id,
    //         recipientId: recipient.id,
    //         channel: NotificationChannel.FCM,
    //         status: NotificationStatus.FAILED,
    //         error: error.message,
    //         metadata: {
    //           errorCode: error.code,
    //           errorDetails: error.details,
    //         },
    //       });

    //       results.push(notificationResult);
    //     }
    //   }

    //   // Update notification status
    //   const hasFailures = results.some(r => r.status === NotificationStatus.FAILED);
    //   const hasSuccess = results.some(r => r.status === NotificationStatus.SENT);

    //   let overallStatus: NotificationStatus;
    //   if (hasSuccess && !hasFailures) {
    //     overallStatus = NotificationStatus.SENT;
    //   } else if (hasSuccess && hasFailures) {
    //     overallStatus = NotificationStatus.PARTIAL;
    //   } else {
    //     overallStatus = NotificationStatus.FAILED;
    //   }

    //   await this.notificationRepository.updateStatus(message.id, overallStatus);

    //   this.logger.log(
    //     `FCM notification processing completed: ${message.id} - Status: ${overallStatus}`
    //   );
    // } catch (error) {
    //   this.logger.error(`Failed to process FCM notification ${message.id}: ${error.message}`);

    //   // Update notification as failed
    //   await this.notificationRepository.updateStatus(message.id, NotificationStatus.FAILED);

    //   // Create failure results for all recipients
    //   for (const recipient of message.notification.recipients) {
    //     if (recipient.channel === NotificationChannel.FCM) {
    //       await this.notificationResultRepository.create({
    //         notificationId: message.id,
    //         recipientId: recipient.id,
    //         channel: NotificationChannel.FCM,
    //         status: NotificationStatus.FAILED,
    //         error: error.message,
    //         metadata: {
    //           processingError: true,
    //         },
    //       });
    //     }
    //   }

    //   throw error; // Re-throw to trigger retry mechanism
    // }
  }

  //   @EventPattern('notification.fcm.retry')
  //   async handleFcmRetry(@Payload() message: any) {
  //     this.logger.log(`Retrying FCM notification: ${message.originalMessage.id}`);

  //     // Check if we've exceeded max retries
  //     if (message.retryCount >= (message.originalMessage.maxRetries || 3)) {
  //       this.logger.error(`Max retries exceeded for FCM notification: ${message.originalMessage.id}`);
  //       await this.notificationRepository.updateStatus(
  //         message.originalMessage.id,
  //         NotificationStatus.FAILED
  //       );
  //       return;
  //     }

  //     // Retry the notification
  //     await this.handleFcmNotification(message.originalMessage);
  //   }
}
