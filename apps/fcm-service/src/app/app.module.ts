import { FcmProvider, INotificationProvider, NotificationService } from '@libs';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { FcmNotificationConsumer } from './consumers/fcm-notificaiton.consumer';
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.development', '.env'],
    }),
  ],
  providers: [{ provide: INotificationProvider, useClass: FcmProvider }, NotificationService],
  controllers: [FcmNotificationConsumer],
})
export class AppModule {}
