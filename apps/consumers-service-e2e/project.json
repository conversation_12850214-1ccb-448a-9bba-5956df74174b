{"name": "consumers-service-e2e", "$schema": "../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["consumers-service"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/consumers-service-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["consumers-service:build", "consumers-service:serve"]}}}