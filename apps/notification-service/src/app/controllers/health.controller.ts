import { Controller, Get, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthService } from '../services/health.service';

export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  uptime: number;
  version: string;
  services: {
    database: {
      status: 'healthy' | 'unhealthy';
      responseTime?: number;
      error?: string;
    };
    rabbitmq: {
      status: 'healthy' | 'unhealthy';
      responseTime?: number;
      error?: string;
    };
    providers: {
      fcm: {
        status: 'healthy' | 'unhealthy' | 'not_configured';
        responseTime?: number;
        error?: string;
      };
      apns: {
        status: 'healthy' | 'unhealthy' | 'not_configured';
        responseTime?: number;
        error?: string;
      };
      email: {
        status: 'healthy' | 'unhealthy' | 'not_configured';
        responseTime?: number;
        error?: string;
      };
      sms: {
        status: 'healthy' | 'unhealthy' | 'not_configured';
        responseTime?: number;
        error?: string;
      };
      wns: {
        status: 'healthy' | 'unhealthy' | 'not_configured';
        responseTime?: number;
        error?: string;
      };
    };
  };
  metrics: {
    totalNotifications: number;
    queuedNotifications: number;
    sentNotifications: number;
    failedNotifications: number;
    averageProcessingTime?: number;
  };
}

@ApiTags('Health')
@Controller('health')
export class HealthController {
  //   constructor(private readonly healthService: HealthService) {}
  //   @Get()
  //   @ApiOperation({ summary: 'Get overall health status' })
  //   @ApiResponse({
  //     status: 200,
  //     description: 'Health check completed successfully',
  //     schema: {
  //       type: 'object',
  //       properties: {
  //         status: { type: 'string', enum: ['healthy', 'unhealthy', 'degraded'] },
  //         timestamp: { type: 'string' },
  //         uptime: { type: 'number' },
  //         version: { type: 'string' },
  //       },
  //     },
  //   })
  //   async getHealth(): Promise<HealthCheckResponse> {
  //     return await this.healthService.getOverallHealth();
  //   }
  //   @Get('detailed')
  //   @ApiOperation({ summary: 'Get detailed health status' })
  //   @ApiResponse({
  //     status: 200,
  //     description: 'Detailed health check completed successfully',
  //   })
  //   async getDetailedHealth(): Promise<HealthCheckResponse> {
  //     return await this.healthService.getDetailedHealth();
  //   }
  //   @Get('database')
  //   @ApiOperation({ summary: 'Get database health status' })
  //   @ApiResponse({
  //     status: 200,
  //     description: 'Database health check completed',
  //   })
  //   async getDatabaseHealth() {
  //     return await this.healthService.checkDatabase();
  //   }
  //   @Get('queue')
  //   @ApiOperation({ summary: 'Get message queue health status' })
  //   @ApiResponse({
  //     status: 200,
  //     description: 'Queue health check completed',
  //   })
  //   async getQueueHealth() {
  //     return await this.healthService.checkQueue();
  //   }
  //   @Get('providers')
  //   @ApiOperation({ summary: 'Get notification providers health status' })
  //   @ApiResponse({
  //     status: 200,
  //     description: 'Providers health check completed',
  //   })
  //   async getProvidersHealth() {
  //     return await this.healthService.checkProviders();
  //   }
  //   @Get('metrics')
  //   @ApiOperation({ summary: 'Get system metrics' })
  //   @ApiResponse({
  //     status: 200,
  //     description: 'System metrics retrieved successfully',
  //   })
  //   async getMetrics() {
  //     return await this.healthService.getMetrics();
  //   }
  //   @Get('ready')
  //   @ApiOperation({ summary: 'Readiness probe for Kubernetes' })
  //   @ApiResponse({
  //     status: 200,
  //     description: 'Service is ready to accept traffic',
  //   })
  //   @ApiResponse({
  //     status: 503,
  //     description: 'Service is not ready',
  //   })
  //   async getReadiness() {
  //     const isReady = await this.healthService.isReady();
  //     if (isReady) {
  //       return {
  //         status: 'ready',
  //         timestamp: new Date().toISOString(),
  //       };
  //     } else {
  //       throw new Error('Service not ready');
  //     }
  //   }
  //   @Get('live')
  //   @ApiOperation({ summary: 'Liveness probe for Kubernetes' })
  //   @ApiResponse({
  //     status: 200,
  //     description: 'Service is alive',
  //   })
  //   async getLiveness() {
  //     return {
  //       status: 'alive',
  //       timestamp: new Date().toISOString(),
  //       uptime: process.uptime(),
  //     };
  //   }
}
