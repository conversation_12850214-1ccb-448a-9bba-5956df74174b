import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
// import { PrismaService } from '@herond-notification-center/database';
import { NotificationService } from './notification.service';
// import { FcmProvider } from '@herond-notification-center/notification-providers/fcm';
// import { ApnsProvider } from '@herond-notification-center/notification-providers/apns';
// import { EmailProvider } from '@herond-notification-center/notification-providers/email';
// import { SmsProvider } from '@herond-notification-center/notification-providers/sms';
// import { WnsProvider } from '@herond-notification-center/notification-providers/wns';
// import { HealthCheckResponse } from '../controllers/health.controller';

@Injectable()
export class HealthService {
  //   private readonly logger = new Logger(HealthService.name);
  //   private readonly startTime = Date.now();
  //   constructor(
  //     private readonly configService: ConfigService,
  //     private readonly prismaService: PrismaService,
  //     private readonly notificationService: NotificationService,
  //     private readonly fcmProvider: FcmProvider,
  //     private readonly apnsProvider: ApnsProvider,
  //     private readonly emailProvider: EmailProvider,
  //     private readonly smsProvider: SmsProvider,
  //     private readonly wnsProvider: WnsProvider,
  //   ) {}
  //   /**
  //    * Get overall health status
  //    */
  //   async getOverallHealth(): Promise<HealthCheckResponse> {
  //     const detailed = await this.getDetailedHealth();
  //     return {
  //       status: detailed.status,
  //       timestamp: detailed.timestamp,
  //       uptime: detailed.uptime,
  //       version: detailed.version,
  //       services: detailed.services,
  //       metrics: detailed.metrics,
  //     };
  //   }
  //   /**
  //    * Get detailed health status
  //    */
  //   async getDetailedHealth(): Promise<HealthCheckResponse> {
  //     const timestamp = new Date().toISOString();
  //     const uptime = Math.floor((Date.now() - this.startTime) / 1000);
  //     const version = process.env.npm_package_version || '1.0.0';
  //     // Check all services
  //     const [database, rabbitmq, providers, metrics] = await Promise.all([
  //       this.checkDatabase(),
  //       this.checkQueue(),
  //       this.checkProviders(),
  //       this.getMetrics(),
  //     ]);
  //     // Determine overall status
  //     let status: 'healthy' | 'unhealthy' | 'degraded' = 'healthy';
  //     if (database.status === 'unhealthy' || rabbitmq.status === 'unhealthy') {
  //       status = 'unhealthy';
  //     } else if (
  //       Object.values(providers).some(provider => provider.status === 'unhealthy') ||
  //       database.status === 'degraded' ||
  //       rabbitmq.status === 'degraded'
  //     ) {
  //       status = 'degraded';
  //     }
  //     return {
  //       status,
  //       timestamp,
  //       uptime,
  //       version,
  //       services: {
  //         database,
  //         rabbitmq,
  //         providers,
  //       },
  //       metrics,
  //     };
  //   }
  //   /**
  //    * Check database health
  //    */
  //   async checkDatabase() {
  //     const startTime = Date.now();
  //     try {
  //       // Simple query to check database connectivity
  //       await this.prismaService.$queryRaw`SELECT 1`;
  //       const responseTime = Date.now() - startTime;
  //       return {
  //         status: 'healthy' as const,
  //         responseTime,
  //       };
  //     } catch (error) {
  //       this.logger.error(`Database health check failed: ${error.message}`);
  //       return {
  //         status: 'unhealthy' as const,
  //         error: error.message,
  //       };
  //     }
  //   }
  //   /**
  //    * Check RabbitMQ health
  //    */
  //   async checkQueue() {
  //     const startTime = Date.now();
  //     try {
  //       // TODO: Implement RabbitMQ health check
  //       // For now, just check if the URL is configured
  //       const rabbitmqUrl = this.configService.get<string>('RABBITMQ_URL');
  //       if (!rabbitmqUrl) {
  //         throw new Error('RABBITMQ_URL not configured');
  //       }
  //       const responseTime = Date.now() - startTime;
  //       return {
  //         status: 'healthy' as const,
  //         responseTime,
  //       };
  //     } catch (error) {
  //       this.logger.error(`Queue health check failed: ${error.message}`);
  //       return {
  //         status: 'unhealthy' as const,
  //         error: error.message,
  //       };
  //     }
  //   }
  //   /**
  //    * Check notification providers health
  //    */
  //   async checkProviders() {
  //     const [fcm, apns, email, sms, wns] = await Promise.all([
  //       this.checkFcmProvider(),
  //       this.checkApnsProvider(),
  //       this.checkEmailProvider(),
  //       this.checkSmsProvider(),
  //       this.checkWnsProvider(),
  //     ]);
  //     return {
  //       fcm,
  //       apns,
  //       email,
  //       sms,
  //       wns,
  //     };
  //   }
  //   /**
  //    * Check FCM provider health
  //    */
  //   private async checkFcmProvider() {
  //     const startTime = Date.now();
  //     try {
  //       const isConfigured = this.fcmProvider.isConfigured();
  //       if (!isConfigured) {
  //         return {
  //           status: 'not_configured' as const,
  //         };
  //       }
  //       // TODO: Implement actual FCM health check
  //       const responseTime = Date.now() - startTime;
  //       return {
  //         status: 'healthy' as const,
  //         responseTime,
  //       };
  //     } catch (error) {
  //       return {
  //         status: 'unhealthy' as const,
  //         error: error.message,
  //       };
  //     }
  //   }
  //   /**
  //    * Check APNS provider health
  //    */
  //   private async checkApnsProvider() {
  //     const startTime = Date.now();
  //     try {
  //       const isConfigured = this.apnsProvider.isConfigured();
  //       if (!isConfigured) {
  //         return {
  //           status: 'not_configured' as const,
  //         };
  //       }
  //       // TODO: Implement actual APNS health check
  //       const responseTime = Date.now() - startTime;
  //       return {
  //         status: 'healthy' as const,
  //         responseTime,
  //       };
  //     } catch (error) {
  //       return {
  //         status: 'unhealthy' as const,
  //         error: error.message,
  //       };
  //     }
  //   }
  //   /**
  //    * Check Email provider health
  //    */
  //   private async checkEmailProvider() {
  //     const startTime = Date.now();
  //     try {
  //       const isConfigured = this.emailProvider.isConfigured();
  //       if (!isConfigured) {
  //         return {
  //           status: 'not_configured' as const,
  //         };
  //       }
  //       // Test SMTP connection
  //       await this.emailProvider.testConnection();
  //       const responseTime = Date.now() - startTime;
  //       return {
  //         status: 'healthy' as const,
  //         responseTime,
  //       };
  //     } catch (error) {
  //       return {
  //         status: 'unhealthy' as const,
  //         error: error.message,
  //       };
  //     }
  //   }
  //   /**
  //    * Check SMS provider health
  //    */
  //   private async checkSmsProvider() {
  //     const startTime = Date.now();
  //     try {
  //       const isConfigured = this.smsProvider.isConfigured();
  //       if (!isConfigured) {
  //         return {
  //           status: 'not_configured' as const,
  //         };
  //       }
  //       // TODO: Implement actual SMS health check
  //       const responseTime = Date.now() - startTime;
  //       return {
  //         status: 'healthy' as const,
  //         responseTime,
  //       };
  //     } catch (error) {
  //       return {
  //         status: 'unhealthy' as const,
  //         error: error.message,
  //       };
  //     }
  //   }
  //   /**
  //    * Check WNS provider health
  //    */
  //   private async checkWnsProvider() {
  //     const startTime = Date.now();
  //     try {
  //       const isConfigured = this.wnsProvider.isConfigured();
  //       if (!isConfigured) {
  //         return {
  //           status: 'not_configured' as const,
  //         };
  //       }
  //       // TODO: Implement actual WNS health check
  //       const responseTime = Date.now() - startTime;
  //       return {
  //         status: 'healthy' as const,
  //         responseTime,
  //       };
  //     } catch (error) {
  //       return {
  //         status: 'unhealthy' as const,
  //         error: error.message,
  //       };
  //     }
  //   }
  //   /**
  //    * Get system metrics
  //    */
  //   async getMetrics() {
  //     try {
  //       const stats = await this.notificationService.getNotificationStats();
  //       return {
  //         totalNotifications: stats.total,
  //         queuedNotifications: stats.queued,
  //         sentNotifications: stats.sent,
  //         failedNotifications: stats.failed,
  //         // TODO: Calculate average processing time
  //         averageProcessingTime: undefined,
  //       };
  //     } catch (error) {
  //       this.logger.error(`Failed to get metrics: ${error.message}`);
  //       return {
  //         totalNotifications: 0,
  //         queuedNotifications: 0,
  //         sentNotifications: 0,
  //         failedNotifications: 0,
  //       };
  //     }
  //   }
  //   /**
  //    * Check if service is ready to accept traffic
  //    */
  //   async isReady(): Promise<boolean> {
  //     try {
  //       const database = await this.checkDatabase();
  //       const queue = await this.checkQueue();
  //       return database.status === 'healthy' && queue.status === 'healthy';
  //     } catch (error) {
  //       this.logger.error(`Readiness check failed: ${error.message}`);
  //       return false;
  //     }
  //   }
}
